// ==UserScript==
// @name         即梦AI & 豆包去水印原图下载
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  去除即梦AI和豆包的水印，支持原图下载，保持高清晰度
// <AUTHOR>
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 样式定义
    const styles = `
        .download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .download-btn:hover {
            background: rgba(0, 0, 0, 0.4);
        }

        .image-container {
            position: relative;
            display: inline-block;
        }

        .image-container:hover .download-btn {
            opacity: 1;
        }
    `;

    // 添加样式到页面
    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    // 获取原图URL的函数
    function getOriginalImageUrl(img) {
        let originalSrc = img.src;

        // 检查懒加载属性
        if (img.dataset.src) originalSrc = img.dataset.src;
        else if (img.dataset.original) originalSrc = img.dataset.original;
        else if (img.dataset.lazy) originalSrc = img.dataset.lazy;

        // 即梦AI的图片处理
        if (window.location.hostname.includes('jimeng.jianying.com')) {
            // 移除阿里云OSS的水印和压缩参数
            originalSrc = originalSrc.replace(/[?&]x-oss-process=[^&]*/g, '')
                                   .replace(/[?&]watermark=[^&]*/g, '')
                                   .replace(/[?&]quality=[^&]*/g, '')
                                   .replace(/[?&]format=[^&]*/g, '')
                                   .replace(/[?&]resize=[^&]*/g, '');

            // 添加高质量参数
            if (!originalSrc.includes('?')) {
                originalSrc += '?x-oss-process=image/format,png/quality,q_100';
            }
        }

        // 豆包的图片处理
        if (window.location.hostname.includes('doubao.com')) {
            // 移除豆包的图片处理参数
            originalSrc = originalSrc.replace(/[?&]x-image-process=[^&]*/g, '')
                                   .replace(/[?&]watermark=[^&]*/g, '')
                                   .replace(/[?&]quality=[^&]*/g, '')
                                   .replace(/[?&]imageView2=[^&]*/g, '')
                                   .replace(/\/resize[^\/]*/g, '');

            // 添加高质量参数
            if (!originalSrc.includes('?')) {
                originalSrc += '?imageView2/2/format/png/q/100';
            }
        }

        return originalSrc;
    }

    // 下载图片函数
    async function downloadImage(originalUrl, filename) {
        try {
            const response = await fetch(originalUrl, {
                headers: {
                    'Referer': window.location.href,
                    'User-Agent': navigator.userAgent
                }
            });

            if (!response.ok) {
                throw new Error(`下载失败: ${response.status}`);
            }

            const blob = await response.blob();
            const sizeInMB = blob.size / 1024 / 1024;

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showNotification(`下载成功！图片大小: ${sizeInMB.toFixed(2)}MB`);

        } catch (error) {
            console.error('下载失败:', error);
            showNotification('下载失败，请重试', 'error');
        }
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 创建下载按钮
    function createDownloadButton(img) {
        const button = document.createElement('button');
        button.className = 'download-btn';
        button.textContent = '下载原图';

        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const originalUrl = getOriginalImageUrl(img);
            const timestamp = new Date().getTime();
            const filename = `image_${timestamp}.png`;

            downloadImage(originalUrl, filename);
        });

        return button;
    }

    // 处理图片元素
    function processImage(img) {
        // 检查是否已经处理过
        if (img.dataset.processed) return;

        // 创建容器
        const container = document.createElement('div');
        container.className = 'image-container';

        // 将图片包装在容器中
        if (img.parentNode) {
            img.parentNode.insertBefore(container, img);
            container.appendChild(img);
        }

        // 创建并添加下载按钮
        const downloadBtn = createDownloadButton(img);
        container.appendChild(downloadBtn);

        // 标记为已处理
        img.dataset.processed = 'true';
    }

    // 查找并处理所有图片
    function processAllImages() {
        const allImages = document.querySelectorAll('img');

        allImages.forEach(img => {
            if (shouldProcessImage(img)) {
                processImage(img);
            }
        });
    }

    // 判断是否应该处理这张图片
    function shouldProcessImage(img) {
        if (!img || !img.src) return false;

        // 检查图片尺寸
        const rect = img.getBoundingClientRect();
        if (rect.width < 100 || rect.height < 100) return false;

        // 检查是否已处理
        if (img.dataset.processed) return false;

        // 排除小图标和按钮
        const excludePatterns = ['icon', 'logo', 'avatar', 'button'];
        const imgSrc = img.src.toLowerCase();
        const imgClass = (img.className || '').toLowerCase();

        for (const pattern of excludePatterns) {
            if (imgSrc.includes(pattern) || imgClass.includes(pattern)) {
                return false;
            }
        }

        return true;
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver(() => {
            processAllImages();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 初始化脚本
    function init() {
        addStyles();
        processAllImages();
        observeChanges();

        // 定期检查新图片
        setInterval(processAllImages, 2000);

        // 显示启动通知
        showNotification('去水印下载脚本已启动！鼠标悬停图片显示下载按钮');
    }

    // 等待页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 1000);
    }

})();
