// ==UserScript==
// @name         即梦AI & 豆包去水印原图下载
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  去除即梦AI和豆包的水印，支持原图下载，保持高清晰度
// <AUTHOR>
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 样式定义
    const styles = `
        .download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        .download-btn:hover {
            background: rgba(0, 0, 0, 0.4);
        }
        
        .image-container {
            position: relative;
            display: inline-block;
        }
        
        .image-container:hover .download-btn {
            opacity: 1;
        }
    `;

    // 添加样式到页面
    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    // 获取原图URL的函数
    function getOriginalImageUrl(img) {
        let src = img.src;

        // 即梦AI的图片处理
        if (window.location.hostname.includes('jimeng.jianying.com')) {
            // 移除所有阿里云OSS处理参数，获取原图
            src = src.split('?')[0]; // 移除所有查询参数

            // 如果有data-original或data-src属性，优先使用
            if (img.dataset.original) {
                src = img.dataset.original;
            } else if (img.dataset.src) {
                src = img.dataset.src;
            }

            // 确保获取最高质量
            if (!src.includes('?')) {
                src += '?x-oss-process=image/quality,q_100/format,png';
            }
        }

        // 豆包的图片处理
        if (window.location.hostname.includes('doubao.com')) {
            // 移除豆包的所有图片处理参数
            src = src.split('?')[0];

            // 检查是否有原图链接
            if (img.dataset.original) {
                src = img.dataset.original;
            } else if (img.dataset.src) {
                src = img.dataset.src;
            }

            // 添加高质量参数
            if (!src.includes('?')) {
                src += '?imageView2/2/format/png/q/100';
            }
        }

        return src;
    }

    // 获取更高质量的图片URL
    function getHighQualityUrl(originalUrl) {
        let url = originalUrl;

        // 对于阿里云OSS
        if (url.includes('aliyuncs.com') || url.includes('oss-cn')) {
            url = url.split('?')[0] + '?x-oss-process=image/quality,q_100/format,png';
        }

        // 对于七牛云
        if (url.includes('qiniu') || url.includes('clouddn.com')) {
            url = url.split('?')[0] + '?imageView2/2/format/png/q/100';
        }

        // 对于腾讯云COS
        if (url.includes('cos.') && url.includes('myqcloud.com')) {
            url = url.split('?')[0] + '?imageView2/2/format/png/q/100';
        }

        return url;
    }

    // 下载图片函数
    async function downloadImage(originalUrl, filename) {
        try {
            let finalUrl = originalUrl;
            let blob;
            let attempts = 0;
            const maxAttempts = 3;

            // 多次尝试获取最佳质量图片
            while (attempts < maxAttempts) {
                attempts++;

                if (attempts === 1) {
                    // 第一次尝试：使用高质量URL
                    finalUrl = getHighQualityUrl(originalUrl);
                } else if (attempts === 2) {
                    // 第二次尝试：移除所有参数
                    finalUrl = originalUrl.split('?')[0];
                } else {
                    // 第三次尝试：使用原始URL
                    finalUrl = originalUrl;
                }

                console.log(`尝试下载 (${attempts}/${maxAttempts}): ${finalUrl}`);

                const response = await fetch(finalUrl, {
                    headers: {
                        'Referer': window.location.href,
                        'User-Agent': navigator.userAgent
                    }
                });

                if (!response.ok) {
                    console.log(`请求失败: ${response.status}`);
                    continue;
                }

                blob = await response.blob();
                const sizeInMB = blob.size / 1024 / 1024;

                console.log(`获取到图片，大小: ${sizeInMB.toFixed(2)}MB`);

                // 如果图片大小在合理范围内（0.5MB-10MB），则使用
                if (sizeInMB >= 0.5 && sizeInMB <= 10) {
                    break;
                } else if (sizeInMB < 0.5 && attempts < maxAttempts) {
                    console.log('图片太小，尝试获取更高质量版本');
                    continue;
                } else {
                    // 即使图片很大也要下载
                    break;
                }
            }

            if (!blob) {
                throw new Error('无法获取图片数据');
            }

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            const finalSizeInMB = blob.size / 1024 / 1024;
            console.log(`下载完成: ${filename}, 最终大小: ${finalSizeInMB.toFixed(2)}MB`);

            // 显示下载成功提示
            showNotification(`下载成功！图片大小: ${finalSizeInMB.toFixed(2)}MB`);

        } catch (error) {
            console.error('下载失败:', error);
            showNotification('下载失败，请重试', 'error');
        }
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 创建下载按钮
    function createDownloadButton(img) {
        const button = document.createElement('button');
        button.className = 'download-btn';
        button.textContent = '下载原图';
        
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const originalUrl = getOriginalImageUrl(img);
            const timestamp = new Date().getTime();
            const filename = `image_${timestamp}.png`;
            
            downloadImage(originalUrl, filename);
        });
        
        return button;
    }

    // 处理图片元素
    function processImage(img) {
        // 检查是否已经处理过
        if (img.dataset.processed) return;
        
        // 创建容器
        const container = document.createElement('div');
        container.className = 'image-container';
        
        // 将图片包装在容器中
        img.parentNode.insertBefore(container, img);
        container.appendChild(img);
        
        // 创建并添加下载按钮
        const downloadBtn = createDownloadButton(img);
        container.appendChild(downloadBtn);
        
        // 标记为已处理
        img.dataset.processed = 'true';
    }

    // 查找并处理所有图片
    function processAllImages() {
        const selectors = [
            // 通用图片选择器
            'img[src*=".png"]',
            'img[src*=".jpg"]',
            'img[src*=".jpeg"]',
            'img[src*=".webp"]',
            // 云服务商图片
            'img[src*="oss-cn"]', // 阿里云OSS图片
            'img[src*="qiniu"]',  // 七牛云图片
            'img[src*="cos."]',   // 腾讯云COS图片
            // 特定网站图片
            'img[src*="jimeng"]', // 即梦AI图片
            'img[src*="doubao"]', // 豆包图片
            'img[src*="jianying"]', // 剪映相关
            // 特定类名或ID的图片
            '.generated-image img',
            '.result-image img',
            '.ai-image img',
            '[class*="image"] img',
            '[id*="image"] img'
        ];

        // 使用Set避免重复处理
        const processedImages = new Set();

        selectors.forEach(selector => {
            try {
                const images = document.querySelectorAll(selector);
                images.forEach(img => {
                    if (!processedImages.has(img) && img.src && img.offsetWidth > 100 && img.offsetHeight > 100) {
                        processedImages.add(img);
                        processImage(img);
                    }
                });
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e);
            }
        });
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // 元素节点
                        if (node.tagName === 'IMG') {
                            processImage(node);
                        } else {
                            // 查找新添加节点中的图片
                            const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                            images.forEach(processImage);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 添加右键菜单功能
    function addContextMenu() {
        document.addEventListener('contextmenu', function(e) {
            if (e.target.tagName === 'IMG' && e.target.src) {
                e.preventDefault();

                const menu = document.createElement('div');
                menu.style.cssText = `
                    position: fixed;
                    left: ${e.pageX}px;
                    top: ${e.pageY}px;
                    background: white;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    padding: 5px 0;
                    z-index: 10001;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                `;

                const downloadOption = document.createElement('div');
                downloadOption.style.cssText = `
                    padding: 8px 15px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #333;
                `;
                downloadOption.textContent = '下载原图';
                downloadOption.addEventListener('click', () => {
                    const originalUrl = getOriginalImageUrl(e.target);
                    const timestamp = new Date().getTime();
                    const filename = `image_${timestamp}.png`;
                    downloadImage(originalUrl, filename);
                    document.body.removeChild(menu);
                });

                downloadOption.addEventListener('mouseenter', () => {
                    downloadOption.style.background = '#f0f0f0';
                });
                downloadOption.addEventListener('mouseleave', () => {
                    downloadOption.style.background = 'white';
                });

                menu.appendChild(downloadOption);
                document.body.appendChild(menu);

                // 点击其他地方关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu() {
                        if (menu.parentNode) {
                            document.body.removeChild(menu);
                        }
                        document.removeEventListener('click', closeMenu);
                    });
                }, 100);
            }
        });
    }

    // 添加快捷键支持
    function addKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+D 下载当前鼠标悬停的图片
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                const hoveredImg = document.querySelector('img:hover');
                if (hoveredImg && hoveredImg.src) {
                    const originalUrl = getOriginalImageUrl(hoveredImg);
                    const timestamp = new Date().getTime();
                    const filename = `image_${timestamp}.png`;
                    downloadImage(originalUrl, filename);
                }
            }
        });
    }

    // 初始化脚本
    function init() {
        console.log('即梦AI & 豆包去水印下载脚本已启动');

        addStyles();
        processAllImages();
        observeChanges();
        addContextMenu();
        addKeyboardShortcuts();

        // 定期检查新图片
        setInterval(processAllImages, 3000);

        // 显示启动通知
        showNotification('去水印下载脚本已启动！鼠标悬停图片显示下载按钮');
    }

    // 等待页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 1000); // 延迟1秒确保页面完全加载
    }

})();
