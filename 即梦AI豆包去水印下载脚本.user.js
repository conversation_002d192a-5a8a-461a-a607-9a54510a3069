// ==UserScript==
// @name         即梦AI & 豆包去水印原图下载
// @namespace    http://tampermonkey.net/
// @version      5.0
// @description  去除即梦AI和豆包的水印，支持原图下载
// <AUTHOR>
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 样式
    const styles = `
        .download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .download-btn:hover {
            background: rgba(0, 0, 0, 0.4);
        }
        .image-container {
            position: relative;
            display: inline-block;
        }
        .image-container:hover .download-btn {
            opacity: 1;
        }
    `;

    // 添加样式
    function addStyles() {
        const style = document.createElement('style');
        style.textContent = styles;
        document.head.appendChild(style);
    }

    // 全局图片URL存储
    const capturedUrls = new Set();

    // 拦截所有网络请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && /\.(jpg|jpeg|png|webp|gif)/.test(url)) {
            capturedUrls.add(url);
        }
        return originalFetch.apply(this, args);
    };

    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        if (typeof url === 'string' && /\.(jpg|jpeg|png|webp|gif)/.test(url)) {
            capturedUrls.add(url);
        }
        return originalXHROpen.call(this, method, url, ...args);
    };

    // 拦截Image对象
    const originalImage = window.Image;
    window.Image = function(...args) {
        const img = new originalImage(...args);
        const originalSrcSetter = Object.getOwnPropertyDescriptor(HTMLImageElement.prototype, 'src').set;
        Object.defineProperty(img, 'src', {
            set: function(value) {
                if (value && /\.(jpg|jpeg|png|webp|gif)/.test(value)) {
                    capturedUrls.add(value);
                }
                originalSrcSetter.call(this, value);
            },
            get: function() {
                return this.getAttribute('src');
            }
        });
        return img;
    };

    // 获取真实图片URL
    function getRealImageUrl(element) {
        // 1. 检查拦截到的URL
        const sortedUrls = Array.from(capturedUrls).sort((a, b) => b.length - a.length);
        for (const url of sortedUrls) {
            if (url.includes('oss-cn') || url.includes('aliyuncs') ||
                url.includes('qiniu') || url.includes('cos.') ||
                url.includes('bytedance') || url.includes('byteimg')) {
                return processImageUrl(url);
            }
        }

        // 2. 检查元素属性
        const sources = [
            element.src,
            element.dataset.src,
            element.dataset.original,
            element.dataset.lazy,
            element.getAttribute('data-src'),
            element.getAttribute('data-original')
        ].filter(Boolean);

        if (sources.length > 0) {
            return processImageUrl(sources[0]);
        }

        // 3. 检查背景图片
        const bgImage = element.style.backgroundImage;
        if (bgImage && bgImage.includes('url(')) {
            const match = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
            if (match && match[1]) {
                return processImageUrl(match[1]);
            }
        }

        // 4. 检查父容器的背景图片
        let parent = element.parentElement;
        while (parent) {
            const parentBg = parent.style.backgroundImage;
            if (parentBg && parentBg.includes('url(')) {
                const match = parentBg.match(/url\(['"]?([^'"]+)['"]?\)/);
                if (match && match[1]) {
                    return processImageUrl(match[1]);
                }
            }
            parent = parent.parentElement;
        }

        return null;
    }

    // 处理图片URL去水印
    function processImageUrl(url) {
        if (!url) return null;

        // 即梦AI处理
        if (window.location.hostname.includes('jimeng.jianying.com')) {
            url = url.replace(/[?&](x-oss-process|watermark|quality|format|resize)=[^&]*/g, '');
            if (!url.includes('?')) {
                url += '?x-oss-process=image/format,png/quality,q_100';
            }
        }

        // 豆包处理
        if (window.location.hostname.includes('doubao.com')) {
            url = url.replace(/[?&](x-image-process|watermark|quality|imageView2)=[^&]*/g, '');
            if (!url.includes('?')) {
                url += '?imageView2/2/format/png/q/100';
            }
        }

        return url;
    }

    // 下载图片
    async function downloadImage(url, filename) {
        if (!url) {
            showNotification('未找到图片URL', 'error');
            return;
        }

        try {
            const response = await fetch(url, {
                headers: {
                    'Referer': window.location.href,
                    'User-Agent': navigator.userAgent
                }
            });

            if (!response.ok) throw new Error('下载失败');

            const blob = await response.blob();
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);

            showNotification(`下载成功！大小: ${(blob.size/1024/1024).toFixed(2)}MB`);
        } catch (error) {
            showNotification('下载失败，请重试', 'error');
        }
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }

    // 创建下载按钮
    function createDownloadButton(element) {
        const button = document.createElement('button');
        button.className = 'download-btn';
        button.textContent = '下载原图';
        button.onclick = (e) => {
            e.preventDefault();
            e.stopPropagation();
            const url = getRealImageUrl(element);
            const filename = `image_${Date.now()}.png`;
            downloadImage(url, filename);
        };
        return button;
    }

    // 处理元素
    function processElement(element) {
        if (element.dataset.processed) return;

        const container = document.createElement('div');
        container.className = 'image-container';

        element.parentNode.insertBefore(container, element);
        container.appendChild(element);
        container.appendChild(createDownloadButton(element));

        element.dataset.processed = 'true';
    }

    // 处理所有可能的图片元素
    function processAllElements() {
        // 处理img元素
        document.querySelectorAll('img').forEach(img => {
            if (img.offsetWidth > 50 && img.offsetHeight > 50 && !img.dataset.processed) {
                processElement(img);
            }
        });

        // 处理背景图片元素
        document.querySelectorAll('div, section, article').forEach(element => {
            const style = window.getComputedStyle(element);
            if (style.backgroundImage && style.backgroundImage !== 'none' &&
                element.offsetWidth > 100 && element.offsetHeight > 100 &&
                !element.dataset.processed) {
                processElement(element);
            }
        });
    }

    // 初始化
    function init() {
        addStyles();

        // 延迟处理，等待页面完全加载
        setTimeout(() => {
            processAllElements();
        }, 2000);

        // 定期检查
        setInterval(processAllElements, 3000);

        // 监听DOM变化
        new MutationObserver(() => {
            setTimeout(processAllElements, 500);
        }).observe(document.body, {
            childList: true,
            subtree: true
        });

        showNotification('去水印下载脚本已启动！');
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 1000);
    }

})();
