// ==UserScript==
// @name         即梦AI & 豆包去水印原图下载
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  去除即梦AI和豆包的水印，支持原图下载，保持高清晰度
// <AUTHOR>
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 样式定义
    const styles = `
        .download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .download-btn:hover {
            background: rgba(0, 0, 0, 0.4);
        }

        .image-container {
            position: relative;
            display: inline-block;
        }

        .image-container:hover .download-btn {
            opacity: 1;
        }
    `;

    // 添加样式到页面
    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    // 拦截网络请求获取真实图片URL
    const imageUrls = new Set();

    // 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && (url.includes('.jpg') || url.includes('.png') || url.includes('.webp') || url.includes('.jpeg'))) {
            imageUrls.add(url);
        }
        return originalFetch.apply(this, args);
    };

    // 拦截XHR请求
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        if (typeof url === 'string' && (url.includes('.jpg') || url.includes('.png') || url.includes('.webp') || url.includes('.jpeg'))) {
            imageUrls.add(url);
        }
        return originalXHROpen.call(this, method, url, ...args);
    };

    // 获取原图URL的函数
    function getOriginalImageUrl(img) {
        let originalSrc = img.src;

        // 1. 检查懒加载属性
        const dataSources = [
            img.dataset.src,
            img.dataset.original,
            img.dataset.lazy,
            img.dataset.lazySrc,
            img.getAttribute('data-src'),
            img.getAttribute('data-original'),
            img.getAttribute('data-lazy-src')
        ].filter(Boolean);

        if (dataSources.length > 0) {
            originalSrc = dataSources[0];
        }

        // 2. 从拦截的请求中查找相关图片
        for (const interceptedUrl of imageUrls) {
            if (interceptedUrl.includes('oss-cn') || interceptedUrl.includes('aliyuncs') ||
                interceptedUrl.includes('qiniu') || interceptedUrl.includes('cos.') ||
                interceptedUrl.includes('bytedance') || interceptedUrl.includes('byteimg')) {
                originalSrc = interceptedUrl;
                break;
            }
        }

        // 3. 查找容器中的背景图片
        const container = img.closest('div, section, article');
        if (container) {
            const bgElements = container.querySelectorAll('[style*="background-image"]');
            for (const element of bgElements) {
                const bgImage = element.style.backgroundImage;
                if (bgImage && bgImage.includes('url(')) {
                    const bgUrl = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
                    if (bgUrl && bgUrl[1]) {
                        originalSrc = bgUrl[1];
                        break;
                    }
                }
            }
        }

        // 4. 即梦AI的图片处理
        if (window.location.hostname.includes('jimeng.jianying.com')) {
            originalSrc = originalSrc.replace(/[?&]x-oss-process=[^&]*/g, '')
                                   .replace(/[?&]watermark=[^&]*/g, '')
                                   .replace(/[?&]quality=[^&]*/g, '')
                                   .replace(/[?&]format=[^&]*/g, '')
                                   .replace(/[?&]resize=[^&]*/g, '');

            if (!originalSrc.includes('?')) {
                originalSrc += '?x-oss-process=image/format,png/quality,q_100';
            }
        }

        // 5. 豆包的图片处理
        if (window.location.hostname.includes('doubao.com')) {
            originalSrc = originalSrc.replace(/[?&]x-image-process=[^&]*/g, '')
                                   .replace(/[?&]watermark=[^&]*/g, '')
                                   .replace(/[?&]quality=[^&]*/g, '')
                                   .replace(/[?&]imageView2=[^&]*/g, '')
                                   .replace(/\/resize[^\/]*/g, '');

            if (!originalSrc.includes('?')) {
                originalSrc += '?imageView2/2/format/png/q/100';
            }
        }

        console.log('原始URL:', img.src);
        console.log('处理后URL:', originalSrc);
        console.log('拦截到的URLs:', Array.from(imageUrls));

        return originalSrc;
    }

    // 下载图片函数
    async function downloadImage(originalUrl, filename) {
        try {
            const response = await fetch(originalUrl, {
                headers: {
                    'Referer': window.location.href,
                    'User-Agent': navigator.userAgent
                }
            });

            if (!response.ok) {
                throw new Error(`下载失败: ${response.status}`);
            }

            const blob = await response.blob();
            const sizeInMB = blob.size / 1024 / 1024;

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showNotification(`下载成功！图片大小: ${sizeInMB.toFixed(2)}MB`);

        } catch (error) {
            console.error('下载失败:', error);
            showNotification('下载失败，请重试', 'error');
        }
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 创建下载按钮
    function createDownloadButton(img) {
        const button = document.createElement('button');
        button.className = 'download-btn';
        button.textContent = '下载原图';

        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const originalUrl = getOriginalImageUrl(img);
            const timestamp = new Date().getTime();
            const filename = `image_${timestamp}.png`;

            downloadImage(originalUrl, filename);
        });

        return button;
    }

    // 处理图片元素
    function processImage(img) {
        // 检查是否已经处理过
        if (img.dataset.processed) return;

        // 创建容器
        const container = document.createElement('div');
        container.className = 'image-container';

        // 将图片包装在容器中
        if (img.parentNode) {
            img.parentNode.insertBefore(container, img);
            container.appendChild(img);
        }

        // 创建并添加下载按钮
        const downloadBtn = createDownloadButton(img);
        container.appendChild(downloadBtn);

        // 标记为已处理
        img.dataset.processed = 'true';
    }

    // 强制加载懒加载图片
    function forceLazyLoad() {
        const lazyImages = document.querySelectorAll('img[data-src], img[data-original], img[data-lazy], img[loading="lazy"]');
        lazyImages.forEach(img => {
            if (img.dataset.src && !img.src) {
                img.src = img.dataset.src;
            } else if (img.dataset.original && !img.src) {
                img.src = img.dataset.original;
            } else if (img.dataset.lazy && !img.src) {
                img.src = img.dataset.lazy;
            }

            // 触发懒加载
            img.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        });
    }

    // 处理背景图片
    function processBackgroundImages() {
        const elementsWithBg = document.querySelectorAll('[style*="background-image"]');
        elementsWithBg.forEach(element => {
            const bgImage = element.style.backgroundImage;
            if (bgImage && bgImage.includes('url(')) {
                const bgUrl = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
                if (bgUrl && bgUrl[1] && element.getBoundingClientRect().width > 100) {
                    // 创建虚拟img元素
                    const virtualImg = document.createElement('img');
                    virtualImg.src = bgUrl[1];
                    virtualImg.style.display = 'none';
                    element.appendChild(virtualImg);

                    // 为背景图片元素添加下载按钮
                    if (!element.dataset.processed) {
                        const container = document.createElement('div');
                        container.className = 'image-container';
                        container.style.position = 'relative';
                        container.style.display = 'inline-block';

                        element.parentNode.insertBefore(container, element);
                        container.appendChild(element);

                        const downloadBtn = createDownloadButton(virtualImg);
                        container.appendChild(downloadBtn);

                        element.dataset.processed = 'true';
                    }
                }
            }
        });
    }

    // 查找并处理所有图片
    function processAllImages() {
        // 强制加载懒加载图片
        forceLazyLoad();

        // 处理背景图片
        processBackgroundImages();

        // 处理普通图片
        const allImages = document.querySelectorAll('img');
        allImages.forEach(img => {
            if (shouldProcessImage(img)) {
                processImage(img);
            }
        });

        console.log(`处理了图片，拦截到 ${imageUrls.size} 个图片URL`);
    }

    // 判断是否应该处理这张图片
    function shouldProcessImage(img) {
        if (!img) return false;

        // 检查是否已处理
        if (img.dataset.processed) return false;

        // 检查是否有图片源（包括懒加载）
        const hasSrc = img.src && img.src !== '' && !img.src.includes('data:image');
        const hasDataSrc = img.dataset.src || img.dataset.original || img.dataset.lazy;

        if (!hasSrc && !hasDataSrc) return false;

        // 检查图片尺寸
        const rect = img.getBoundingClientRect();
        if (rect.width < 50 || rect.height < 50) return false;

        // 排除小图标和按钮
        const excludePatterns = ['icon', 'logo', 'avatar', 'button'];
        const imgSrc = (img.src || '').toLowerCase();
        const imgClass = (img.className || '').toLowerCase();

        for (const pattern of excludePatterns) {
            if (imgSrc.includes(pattern) || imgClass.includes(pattern)) {
                return false;
            }
        }

        return true;
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver(() => {
            processAllImages();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 添加调试按钮（临时）
    function addDebugButton() {
        const debugBtn = document.createElement('div');
        debugBtn.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            z-index: 10002;
        `;
        debugBtn.textContent = '调试';
        debugBtn.addEventListener('click', () => {
            console.log('=== 调试信息 ===');
            console.log('拦截到的图片URLs:', Array.from(imageUrls));

            const allImages = document.querySelectorAll('img');
            const backgroundImages = document.querySelectorAll('[style*="background-image"]');

            console.log(`页面img元素: ${allImages.length}`);
            console.log(`背景图片元素: ${backgroundImages.length}`);

            allImages.forEach((img, index) => {
                console.log(`图片${index + 1}:`, {
                    src: img.src,
                    dataSrc: img.dataset.src,
                    dataOriginal: img.dataset.original,
                    width: img.getBoundingClientRect().width,
                    height: img.getBoundingClientRect().height,
                    shouldProcess: shouldProcessImage(img)
                });
            });

            backgroundImages.forEach((element, index) => {
                console.log(`背景图片${index + 1}:`, element.style.backgroundImage);
            });
        });

        document.body.appendChild(debugBtn);
    }

    // 初始化脚本
    function init() {
        addStyles();
        addDebugButton(); // 临时调试

        // 延迟处理，确保页面完全加载
        setTimeout(() => {
            processAllImages();
        }, 2000);

        observeChanges();

        // 定期检查新图片
        setInterval(processAllImages, 3000);

        // 显示启动通知
        showNotification('去水印下载脚本已启动！点击左上角红色按钮查看调试信息');
    }

    // 等待页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 1000);
    }

})();
