// ==UserScript==
// @name         即梦AI & 豆包去水印原图下载
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  去除即梦AI和豆包的水印，支持原图下载，保持高清晰度
// <AUTHOR>
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 样式定义
    const styles = `
        .download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        .download-btn:hover {
            background: rgba(0, 0, 0, 0.4);
        }
        
        .image-container {
            position: relative;
            display: inline-block;
        }
        
        .image-container:hover .download-btn {
            opacity: 1;
        }
    `;

    // 添加样式到页面
    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    // 从网络请求中拦截图片URL
    function interceptImageRequests() {
        const originalFetch = window.fetch;
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const imageUrls = new Set();

        // 拦截fetch请求
        window.fetch = function(...args) {
            const url = args[0];
            if (typeof url === 'string' && (url.includes('.jpg') || url.includes('.png') || url.includes('.webp') || url.includes('.jpeg'))) {
                imageUrls.add(url);
                console.log('拦截到图片请求:', url);
            }
            return originalFetch.apply(this, args);
        };

        // 拦截XHR请求
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            if (typeof url === 'string' && (url.includes('.jpg') || url.includes('.png') || url.includes('.webp') || url.includes('.jpeg'))) {
                imageUrls.add(url);
                console.log('拦截到XHR图片请求:', url);
            }
            return originalXHROpen.call(this, method, url, ...args);
        };

        return imageUrls;
    }

    // 全局图片URL存储
    const interceptedImageUrls = interceptImageRequests();

    // 获取原图URL的函数
    function getOriginalImageUrl(img) {
        let src = img.src;
        let originalSrc = src;

        // 检查所有可能的原图属性
        const possibleSources = [
            img.dataset.original,
            img.dataset.src,
            img.dataset.originSrc,
            img.dataset.fullSrc,
            img.dataset.lazy,
            img.dataset.lazySrc,
            img.getAttribute('data-original'),
            img.getAttribute('data-src'),
            img.getAttribute('data-origin-src'),
            img.getAttribute('data-full-src'),
            img.getAttribute('data-lazy'),
            img.getAttribute('data-lazy-src'),
            img.getAttribute('src-original'),
            img.getAttribute('original-src'),
            img.getAttribute('lazy-src')
        ].filter(Boolean);

        if (possibleSources.length > 0) {
            originalSrc = possibleSources[0];
            console.log('找到原图属性:', originalSrc);
        }

        // 尝试从拦截的请求中找到相关图片
        for (const interceptedUrl of interceptedImageUrls) {
            if (interceptedUrl.includes('oss-cn') || interceptedUrl.includes('aliyuncs') ||
                interceptedUrl.includes('qiniu') || interceptedUrl.includes('cos.') ||
                interceptedUrl.includes('bytedance') || interceptedUrl.includes('byteimg')) {
                originalSrc = interceptedUrl;
                console.log('使用拦截的URL:', originalSrc);
                break;
            }
        }

        // 即梦AI的图片处理
        if (window.location.hostname.includes('jimeng.jianying.com')) {
            // 尝试从页面中找到相关的原图链接
            const imgContainer = img.closest('div, section, article, [class*="image"], [class*="result"], [class*="generated"]');
            if (imgContainer) {
                // 查找所有可能的图片元素
                const allImgsInContainer = imgContainer.querySelectorAll('img, [style*="background-image"]');
                allImgsInContainer.forEach(element => {
                    if (element !== img) {
                        const bgImage = element.style.backgroundImage;
                        if (bgImage && bgImage.includes('url(')) {
                            const bgUrl = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
                            if (bgUrl && bgUrl[1]) {
                                originalSrc = bgUrl[1];
                                console.log('找到背景图片:', originalSrc);
                            }
                        } else if (element.src && element.src !== img.src) {
                            originalSrc = element.src;
                            console.log('找到容器内其他图片:', originalSrc);
                        }
                    }
                });
            }

            // 移除阿里云OSS的水印和压缩参数
            originalSrc = originalSrc.replace(/[?&]x-oss-process=[^&]*/g, '')
                                   .replace(/[?&]watermark=[^&]*/g, '')
                                   .replace(/[?&]quality=[^&]*/g, '')
                                   .replace(/[?&]format=[^&]*/g, '')
                                   .replace(/[?&]resize=[^&]*/g, '');

            // 如果URL中没有参数，添加高质量参数
            if (!originalSrc.includes('?')) {
                originalSrc += '?x-oss-process=image/format,png/quality,q_100';
            } else {
                originalSrc += '&x-oss-process=image/format,png/quality,q_100';
            }
        }

        // 豆包的图片处理
        if (window.location.hostname.includes('doubao.com')) {
            // 尝试从页面中找到相关的原图链接
            const imgContainer = img.closest('div, section, article, [class*="image"], [class*="result"], [class*="generated"], [class*="chat"]');
            if (imgContainer) {
                const allImgsInContainer = imgContainer.querySelectorAll('img, [style*="background-image"]');
                allImgsInContainer.forEach(element => {
                    if (element !== img) {
                        const bgImage = element.style.backgroundImage;
                        if (bgImage && bgImage.includes('url(')) {
                            const bgUrl = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
                            if (bgUrl && bgUrl[1]) {
                                originalSrc = bgUrl[1];
                                console.log('找到背景图片:', originalSrc);
                            }
                        } else if (element.src && element.src !== img.src) {
                            originalSrc = element.src;
                            console.log('找到容器内其他图片:', originalSrc);
                        }
                    }
                });
            }

            // 移除豆包的图片处理参数
            originalSrc = originalSrc.replace(/[?&]x-image-process=[^&]*/g, '')
                                   .replace(/[?&]watermark=[^&]*/g, '')
                                   .replace(/[?&]quality=[^&]*/g, '')
                                   .replace(/[?&]imageView2=[^&]*/g, '')
                                   .replace(/\/resize[^\/]*/g, '');

            // 添加高质量参数
            if (!originalSrc.includes('?')) {
                originalSrc += '?imageView2/2/format/png/q/100';
            } else {
                originalSrc += '&imageView2/2/format/png/q/100';
            }
        }

        console.log('原始URL:', src);
        console.log('处理后URL:', originalSrc);

        return originalSrc;
    }

    // 获取更高质量的图片URL
    function getHighQualityUrl(originalUrl) {
        let url = originalUrl;

        // 对于阿里云OSS
        if (url.includes('aliyuncs.com') || url.includes('oss-cn')) {
            url = url.split('?')[0] + '?x-oss-process=image/format,png/quality,q_100';
        }

        // 对于七牛云
        if (url.includes('qiniu') || url.includes('clouddn.com')) {
            url = url.split('?')[0] + '?imageView2/2/format/png/q/100';
        }

        // 对于腾讯云COS
        if (url.includes('cos.') && url.includes('myqcloud.com')) {
            url = url.split('?')[0] + '?imageView2/2/format/png/q/100';
        }

        // 对于字节跳动的图片服务
        if (url.includes('bytedance') || url.includes('byteimg') || url.includes('pstatp')) {
            url = url.split('?')[0] + '?format=png&quality=100';
        }

        return url;
    }

    // 尝试多种方式获取原图
    async function tryMultipleUrls(baseUrl) {
        const urlVariants = [
            baseUrl,
            baseUrl.split('?')[0], // 移除所有参数
            baseUrl.replace(/\/w_\d+/, '').replace(/\/h_\d+/, ''), // 移除宽高限制
            baseUrl.replace(/\/c_\d+/, ''), // 移除裁剪参数
            baseUrl.replace(/\/q_\d+/, '/q_100'), // 设置最高质量
            getHighQualityUrl(baseUrl)
        ];

        // 去重
        const uniqueUrls = [...new Set(urlVariants)];

        for (const url of uniqueUrls) {
            try {
                const response = await fetch(url, {
                    method: 'HEAD',
                    headers: {
                        'Referer': window.location.href,
                        'User-Agent': navigator.userAgent
                    }
                });

                if (response.ok) {
                    const contentLength = response.headers.get('content-length');
                    const sizeInMB = contentLength ? parseInt(contentLength) / 1024 / 1024 : 0;

                    console.log(`URL: ${url}, 大小: ${sizeInMB.toFixed(2)}MB`);

                    // 如果图片大小合理，返回这个URL
                    if (sizeInMB > 0.5) {
                        return url;
                    }
                }
            } catch (e) {
                console.log(`URL ${url} 检查失败:`, e);
            }
        }

        // 如果都失败了，返回第一个URL
        return uniqueUrls[0];
    }

    // 下载图片函数
    async function downloadImage(originalUrl, filename) {
        try {
            console.log('开始下载图片:', originalUrl);

            // 使用多种URL尝试获取最佳图片
            const bestUrl = await tryMultipleUrls(originalUrl);
            console.log('选择的最佳URL:', bestUrl);

            const response = await fetch(bestUrl, {
                headers: {
                    'Referer': window.location.href,
                    'User-Agent': navigator.userAgent,
                    'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const blob = await response.blob();
            const sizeInMB = blob.size / 1024 / 1024;

            console.log(`获取到图片，大小: ${sizeInMB.toFixed(2)}MB, 类型: ${blob.type}`);

            // 如果图片太小，尝试其他方法
            if (sizeInMB < 0.1) {
                console.log('图片太小，尝试直接访问原始URL');
                const directResponse = await fetch(originalUrl.split('?')[0], {
                    headers: {
                        'Referer': window.location.href,
                        'User-Agent': navigator.userAgent
                    }
                });

                if (directResponse.ok) {
                    const directBlob = await directResponse.blob();
                    const directSizeInMB = directBlob.size / 1024 / 1024;

                    if (directSizeInMB > sizeInMB) {
                        console.log(`使用直接访问的图片，大小: ${directSizeInMB.toFixed(2)}MB`);
                        downloadBlob(directBlob, filename);
                        showNotification(`下载成功！图片大小: ${directSizeInMB.toFixed(2)}MB`);
                        return;
                    }
                }
            }

            downloadBlob(blob, filename);
            showNotification(`下载成功！图片大小: ${sizeInMB.toFixed(2)}MB`);

        } catch (error) {
            console.error('下载失败:', error);
            showNotification(`下载失败: ${error.message}`, 'error');
        }
    }

    // 下载Blob对象
    function downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        console.log(`下载完成: ${filename}`);
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 创建下载按钮
    function createDownloadButton(img) {
        const button = document.createElement('button');
        button.className = 'download-btn';
        button.textContent = '下载原图';
        
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const originalUrl = getOriginalImageUrl(img);
            const timestamp = new Date().getTime();
            const filename = `image_${timestamp}.png`;
            
            downloadImage(originalUrl, filename);
        });
        
        return button;
    }

    // 处理图片元素
    function processImage(img, targetElement = null) {
        // 检查是否已经处理过
        if (img.dataset.processed) return;

        const target = targetElement || img;

        // 创建容器
        const container = document.createElement('div');
        container.className = 'image-container';

        // 将图片包装在容器中
        if (target.parentNode) {
            target.parentNode.insertBefore(container, target);
            container.appendChild(target);
        }

        // 创建并添加下载按钮
        const downloadBtn = createDownloadButton(img);
        container.appendChild(downloadBtn);

        // 标记为已处理
        img.dataset.processed = 'true';
        if (targetElement) {
            targetElement.dataset.processed = 'true';
        }
    }

    // 查找并处理所有图片
    function processAllImages() {
        console.log('开始处理图片...');

        // 首先强制加载懒加载图片
        forceLazyLoad();

        // 处理背景图片
        processBackgroundImages();

        // 更全面的选择器
        const selectors = [
            // 基本图片选择器
            'img',
            // 懒加载图片
            'img[data-src]',
            'img[data-original]',
            'img[data-lazy]',
            'img[loading="lazy"]',
            // 特定格式图片
            'img[src*=".png"]',
            'img[src*=".jpg"]',
            'img[src*=".jpeg"]',
            'img[src*=".webp"]',
            'img[src*=".gif"]',
            // 云服务商图片
            'img[src*="oss-cn"]',
            'img[src*="aliyuncs.com"]',
            'img[src*="qiniu"]',
            'img[src*="clouddn.com"]',
            'img[src*="cos."]',
            'img[src*="myqcloud.com"]',
            'img[src*="bytedance"]',
            'img[src*="byteimg"]',
            'img[src*="pstatp"]',
            // 特定网站图片
            'img[src*="jimeng"]',
            'img[src*="doubao"]',
            'img[src*="jianying"]',
            // 可能的容器内图片
            '[class*="image"] img',
            '[class*="result"] img',
            '[class*="generated"] img',
            '[class*="ai"] img',
            '[class*="chat"] img',
            '[class*="message"] img',
            '[id*="image"] img',
            '[id*="result"] img'
        ];

        // 使用Set避免重复处理
        const processedImages = new Set();

        // 首先处理所有img标签
        const allImages = document.querySelectorAll('img');
        console.log(`找到 ${allImages.length} 个img元素`);

        allImages.forEach((img, index) => {
            if (shouldProcessImage(img) && !processedImages.has(img)) {
                processedImages.add(img);
                processImage(img);
                console.log(`处理图片 ${index + 1}: ${img.src || img.dataset.src || '无源'}`);
            }
        });

        // 然后处理特定选择器
        selectors.forEach(selector => {
            try {
                const images = document.querySelectorAll(selector);
                images.forEach(img => {
                    if (shouldProcessImage(img) && !processedImages.has(img)) {
                        processedImages.add(img);
                        processImage(img);
                    }
                });
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e);
            }
        });

        console.log(`总共处理了 ${processedImages.size} 张图片`);
    }

    // 判断是否应该处理这张图片
    function shouldProcessImage(img) {
        if (!img) return false;

        // 检查是否有任何图片源
        const hasSrc = img.src && img.src !== '' && !img.src.includes('data:image');
        const hasDataSrc = img.dataset.src || img.dataset.original || img.dataset.lazy;
        const hasBackgroundImage = img.style.backgroundImage && img.style.backgroundImage.includes('url(');

        if (!hasSrc && !hasDataSrc && !hasBackgroundImage) {
            // 检查是否是占位符图片，但有潜在的真实图片
            const container = img.closest('div, section, article');
            if (container) {
                const hasImageInContainer = container.querySelector('[style*="background-image"], img[data-src], img[data-original]');
                if (!hasImageInContainer) return false;
            } else {
                return false;
            }
        }

        // 检查图片尺寸
        const rect = img.getBoundingClientRect();
        if (rect.width < 30 || rect.height < 30) return false;

        // 检查图片是否可见（但允许懒加载图片）
        const style = window.getComputedStyle(img);
        if (style.display === 'none') {
            return false;
        }

        // 排除一些不需要的图片
        const excludePatterns = [
            'avatar',
            'icon',
            'logo',
            'button',
            'loading',
            'spinner'
        ];

        const imgSrc = (img.src || '').toLowerCase();
        const imgClass = (img.className || '').toLowerCase();
        const imgId = (img.id || '').toLowerCase();

        for (const pattern of excludePatterns) {
            if (imgSrc.includes(pattern) || imgClass.includes(pattern) || imgId.includes(pattern)) {
                return false;
            }
        }

        return true;
    }

    // 处理背景图片
    function processBackgroundImages() {
        const elementsWithBg = document.querySelectorAll('[style*="background-image"]');
        elementsWithBg.forEach(element => {
            const bgImage = element.style.backgroundImage;
            if (bgImage && bgImage.includes('url(')) {
                const bgUrl = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
                if (bgUrl && bgUrl[1]) {
                    // 创建一个虚拟img元素来处理背景图片
                    const virtualImg = document.createElement('img');
                    virtualImg.src = bgUrl[1];
                    virtualImg.style.display = 'none';
                    element.appendChild(virtualImg);

                    // 为背景图片元素添加下载功能
                    if (element.getBoundingClientRect().width > 100 && element.getBoundingClientRect().height > 100) {
                        processImage(virtualImg, element);
                    }
                }
            }
        });
    }

    // 强制加载懒加载图片
    function forceLazyLoad() {
        const lazyImages = document.querySelectorAll('img[data-src], img[data-original], img[data-lazy], img[loading="lazy"]');
        lazyImages.forEach(img => {
            if (img.dataset.src && !img.src) {
                img.src = img.dataset.src;
            } else if (img.dataset.original && !img.src) {
                img.src = img.dataset.original;
            } else if (img.dataset.lazy && !img.src) {
                img.src = img.dataset.lazy;
            }

            // 触发懒加载
            img.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

            // 模拟滚动事件
            const scrollEvent = new Event('scroll');
            window.dispatchEvent(scrollEvent);
        });
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // 元素节点
                        if (node.tagName === 'IMG') {
                            processImage(node);
                        } else {
                            // 查找新添加节点中的图片
                            const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                            images.forEach(processImage);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 添加右键菜单功能
    function addContextMenu() {
        document.addEventListener('contextmenu', function(e) {
            if (e.target.tagName === 'IMG' && e.target.src) {
                e.preventDefault();

                const menu = document.createElement('div');
                menu.style.cssText = `
                    position: fixed;
                    left: ${e.pageX}px;
                    top: ${e.pageY}px;
                    background: white;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    padding: 5px 0;
                    z-index: 10001;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                `;

                const downloadOption = document.createElement('div');
                downloadOption.style.cssText = `
                    padding: 8px 15px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #333;
                `;
                downloadOption.textContent = '下载原图';
                downloadOption.addEventListener('click', () => {
                    const originalUrl = getOriginalImageUrl(e.target);
                    const timestamp = new Date().getTime();
                    const filename = `image_${timestamp}.png`;
                    downloadImage(originalUrl, filename);
                    document.body.removeChild(menu);
                });

                downloadOption.addEventListener('mouseenter', () => {
                    downloadOption.style.background = '#f0f0f0';
                });
                downloadOption.addEventListener('mouseleave', () => {
                    downloadOption.style.background = 'white';
                });

                menu.appendChild(downloadOption);
                document.body.appendChild(menu);

                // 点击其他地方关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu() {
                        if (menu.parentNode) {
                            document.body.removeChild(menu);
                        }
                        document.removeEventListener('click', closeMenu);
                    });
                }, 100);
            }
        });
    }

    // 添加快捷键支持
    function addKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+D 下载当前鼠标悬停的图片
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                const hoveredImg = document.querySelector('img:hover');
                if (hoveredImg && hoveredImg.src) {
                    const originalUrl = getOriginalImageUrl(hoveredImg);
                    const timestamp = new Date().getTime();
                    const filename = `image_${timestamp}.png`;
                    downloadImage(originalUrl, filename);
                }
            }
        });
    }

    // 添加调试功能
    function addDebugFeatures() {
        // 添加调试按钮
        const debugBtn = document.createElement('div');
        debugBtn.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            z-index: 10002;
        `;
        debugBtn.textContent = '调试模式';
        debugBtn.addEventListener('click', () => {
            console.log('=== 调试信息 ===');

            const allImages = document.querySelectorAll('img');
            const backgroundImages = document.querySelectorAll('[style*="background-image"]');
            const lazyImages = document.querySelectorAll('img[data-src], img[data-original], img[data-lazy]');

            console.log(`页面总共有 ${allImages.length} 张img元素`);
            console.log(`页面总共有 ${backgroundImages.length} 个背景图片元素`);
            console.log(`页面总共有 ${lazyImages.length} 张懒加载图片`);
            console.log(`拦截到 ${interceptedImageUrls.size} 个图片请求`);

            // 显示拦截到的图片URL
            console.log('拦截到的图片URL:');
            interceptedImageUrls.forEach(url => console.log('  -', url));

            allImages.forEach((img, index) => {
                const rect = img.getBoundingClientRect();
                console.log(`图片 ${index + 1}:`, {
                    src: img.src || '无src',
                    dataSrc: img.dataset.src || '无data-src',
                    dataOriginal: img.dataset.original || '无data-original',
                    width: rect.width,
                    height: rect.height,
                    offsetWidth: img.offsetWidth,
                    offsetHeight: img.offsetHeight,
                    className: img.className || '无class',
                    id: img.id || '无id',
                    style: img.style.cssText || '无style',
                    shouldProcess: shouldProcessImage(img),
                    processed: img.dataset.processed || '未处理'
                });

                // 给符合条件的图片添加红色边框
                if (shouldProcessImage(img)) {
                    img.style.border = '3px solid red';
                    img.style.boxShadow = '0 0 10px red';
                    setTimeout(() => {
                        img.style.border = '';
                        img.style.boxShadow = '';
                    }, 5000);
                } else {
                    // 给不符合条件的图片添加蓝色边框
                    img.style.border = '1px solid blue';
                    setTimeout(() => {
                        img.style.border = '';
                    }, 3000);
                }
            });

            // 显示背景图片信息
            backgroundImages.forEach((element, index) => {
                const bgImage = element.style.backgroundImage;
                console.log(`背景图片 ${index + 1}:`, {
                    backgroundImage: bgImage,
                    className: element.className,
                    id: element.id
                });
            });

            showNotification(`调试完成！红框=可处理，蓝框=不可处理。详情查看控制台`);
        });

        document.body.appendChild(debugBtn);
    }

    // 初始化脚本
    function init() {
        console.log('即梦AI & 豆包去水印下载脚本已启动');

        addStyles();
        addDebugFeatures(); // 添加调试功能
        processAllImages();
        observeChanges();
        addContextMenu();
        addKeyboardShortcuts();

        // 定期检查新图片
        setInterval(processAllImages, 3000);

        // 显示启动通知
        showNotification('去水印下载脚本已启动！鼠标悬停图片显示下载按钮，点击左上角红色按钮进行调试');
    }

    // 等待页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 1000); // 延迟1秒确保页面完全加载
    }

})();
