// ==UserScript==
// @name         即梦AI & 豆包去水印原图下载
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  去除即梦AI和豆包的水印，支持原图下载，保持高清晰度
// <AUTHOR>
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 样式定义
    const styles = `
        .download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        .download-btn:hover {
            background: rgba(0, 0, 0, 0.4);
        }
        
        .image-container {
            position: relative;
            display: inline-block;
        }
        
        .image-container:hover .download-btn {
            opacity: 1;
        }
    `;

    // 添加样式到页面
    function addStyles() {
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    // 获取原图URL的函数
    function getOriginalImageUrl(img) {
        let src = img.src;
        let originalSrc = src;

        // 检查所有可能的原图属性
        const possibleSources = [
            img.dataset.original,
            img.dataset.src,
            img.dataset.originSrc,
            img.dataset.fullSrc,
            img.getAttribute('data-original'),
            img.getAttribute('data-src'),
            img.getAttribute('data-origin-src'),
            img.getAttribute('data-full-src'),
            img.getAttribute('src-original'),
            img.getAttribute('original-src')
        ].filter(Boolean);

        if (possibleSources.length > 0) {
            originalSrc = possibleSources[0];
        }

        // 即梦AI的图片处理
        if (window.location.hostname.includes('jimeng.jianying.com')) {
            // 尝试从页面中找到相关的原图链接
            const imgContainer = img.closest('[class*="image"], [class*="result"], [class*="generated"]');
            if (imgContainer) {
                const hiddenImg = imgContainer.querySelector('img[style*="display: none"], img[style*="visibility: hidden"]');
                if (hiddenImg && hiddenImg.src) {
                    originalSrc = hiddenImg.src;
                }
            }

            // 移除阿里云OSS的水印和压缩参数
            originalSrc = originalSrc.replace(/[?&]x-oss-process=[^&]*/g, '')
                                   .replace(/[?&]watermark=[^&]*/g, '')
                                   .replace(/[?&]quality=[^&]*/g, '')
                                   .replace(/[?&]format=[^&]*/g, '')
                                   .replace(/[?&]resize=[^&]*/g, '');

            // 如果URL中没有参数，添加高质量参数
            if (!originalSrc.includes('?')) {
                originalSrc += '?x-oss-process=image/format,png/quality,q_100';
            } else {
                originalSrc += '&x-oss-process=image/format,png/quality,q_100';
            }
        }

        // 豆包的图片处理
        if (window.location.hostname.includes('doubao.com')) {
            // 尝试从页面中找到相关的原图链接
            const imgContainer = img.closest('[class*="image"], [class*="result"], [class*="generated"], [class*="chat"]');
            if (imgContainer) {
                const hiddenImg = imgContainer.querySelector('img[style*="display: none"], img[style*="visibility: hidden"]');
                if (hiddenImg && hiddenImg.src) {
                    originalSrc = hiddenImg.src;
                }
            }

            // 移除豆包的图片处理参数
            originalSrc = originalSrc.replace(/[?&]x-image-process=[^&]*/g, '')
                                   .replace(/[?&]watermark=[^&]*/g, '')
                                   .replace(/[?&]quality=[^&]*/g, '')
                                   .replace(/[?&]imageView2=[^&]*/g, '')
                                   .replace(/\/resize[^\/]*/g, '');

            // 添加高质量参数
            if (!originalSrc.includes('?')) {
                originalSrc += '?imageView2/2/format/png/q/100';
            } else {
                originalSrc += '&imageView2/2/format/png/q/100';
            }
        }

        console.log('原始URL:', src);
        console.log('处理后URL:', originalSrc);

        return originalSrc;
    }

    // 获取更高质量的图片URL
    function getHighQualityUrl(originalUrl) {
        let url = originalUrl;

        // 对于阿里云OSS
        if (url.includes('aliyuncs.com') || url.includes('oss-cn')) {
            url = url.split('?')[0] + '?x-oss-process=image/format,png/quality,q_100';
        }

        // 对于七牛云
        if (url.includes('qiniu') || url.includes('clouddn.com')) {
            url = url.split('?')[0] + '?imageView2/2/format/png/q/100';
        }

        // 对于腾讯云COS
        if (url.includes('cos.') && url.includes('myqcloud.com')) {
            url = url.split('?')[0] + '?imageView2/2/format/png/q/100';
        }

        // 对于字节跳动的图片服务
        if (url.includes('bytedance') || url.includes('byteimg') || url.includes('pstatp')) {
            url = url.split('?')[0] + '?format=png&quality=100';
        }

        return url;
    }

    // 尝试多种方式获取原图
    async function tryMultipleUrls(baseUrl) {
        const urlVariants = [
            baseUrl,
            baseUrl.split('?')[0], // 移除所有参数
            baseUrl.replace(/\/w_\d+/, '').replace(/\/h_\d+/, ''), // 移除宽高限制
            baseUrl.replace(/\/c_\d+/, ''), // 移除裁剪参数
            baseUrl.replace(/\/q_\d+/, '/q_100'), // 设置最高质量
            getHighQualityUrl(baseUrl)
        ];

        // 去重
        const uniqueUrls = [...new Set(urlVariants)];

        for (const url of uniqueUrls) {
            try {
                const response = await fetch(url, {
                    method: 'HEAD',
                    headers: {
                        'Referer': window.location.href,
                        'User-Agent': navigator.userAgent
                    }
                });

                if (response.ok) {
                    const contentLength = response.headers.get('content-length');
                    const sizeInMB = contentLength ? parseInt(contentLength) / 1024 / 1024 : 0;

                    console.log(`URL: ${url}, 大小: ${sizeInMB.toFixed(2)}MB`);

                    // 如果图片大小合理，返回这个URL
                    if (sizeInMB > 0.5) {
                        return url;
                    }
                }
            } catch (e) {
                console.log(`URL ${url} 检查失败:`, e);
            }
        }

        // 如果都失败了，返回第一个URL
        return uniqueUrls[0];
    }

    // 下载图片函数
    async function downloadImage(originalUrl, filename) {
        try {
            console.log('开始下载图片:', originalUrl);

            // 使用多种URL尝试获取最佳图片
            const bestUrl = await tryMultipleUrls(originalUrl);
            console.log('选择的最佳URL:', bestUrl);

            const response = await fetch(bestUrl, {
                headers: {
                    'Referer': window.location.href,
                    'User-Agent': navigator.userAgent,
                    'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const blob = await response.blob();
            const sizeInMB = blob.size / 1024 / 1024;

            console.log(`获取到图片，大小: ${sizeInMB.toFixed(2)}MB, 类型: ${blob.type}`);

            // 如果图片太小，尝试其他方法
            if (sizeInMB < 0.1) {
                console.log('图片太小，尝试直接访问原始URL');
                const directResponse = await fetch(originalUrl.split('?')[0], {
                    headers: {
                        'Referer': window.location.href,
                        'User-Agent': navigator.userAgent
                    }
                });

                if (directResponse.ok) {
                    const directBlob = await directResponse.blob();
                    const directSizeInMB = directBlob.size / 1024 / 1024;

                    if (directSizeInMB > sizeInMB) {
                        console.log(`使用直接访问的图片，大小: ${directSizeInMB.toFixed(2)}MB`);
                        downloadBlob(directBlob, filename);
                        showNotification(`下载成功！图片大小: ${directSizeInMB.toFixed(2)}MB`);
                        return;
                    }
                }
            }

            downloadBlob(blob, filename);
            showNotification(`下载成功！图片大小: ${sizeInMB.toFixed(2)}MB`);

        } catch (error) {
            console.error('下载失败:', error);
            showNotification(`下载失败: ${error.message}`, 'error');
        }
    }

    // 下载Blob对象
    function downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        console.log(`下载完成: ${filename}`);
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 创建下载按钮
    function createDownloadButton(img) {
        const button = document.createElement('button');
        button.className = 'download-btn';
        button.textContent = '下载原图';
        
        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const originalUrl = getOriginalImageUrl(img);
            const timestamp = new Date().getTime();
            const filename = `image_${timestamp}.png`;
            
            downloadImage(originalUrl, filename);
        });
        
        return button;
    }

    // 处理图片元素
    function processImage(img) {
        // 检查是否已经处理过
        if (img.dataset.processed) return;
        
        // 创建容器
        const container = document.createElement('div');
        container.className = 'image-container';
        
        // 将图片包装在容器中
        img.parentNode.insertBefore(container, img);
        container.appendChild(img);
        
        // 创建并添加下载按钮
        const downloadBtn = createDownloadButton(img);
        container.appendChild(downloadBtn);
        
        // 标记为已处理
        img.dataset.processed = 'true';
    }

    // 查找并处理所有图片
    function processAllImages() {
        // 更全面的选择器
        const selectors = [
            // 基本图片选择器
            'img',
            // 特定格式图片
            'img[src*=".png"]',
            'img[src*=".jpg"]',
            'img[src*=".jpeg"]',
            'img[src*=".webp"]',
            'img[src*=".gif"]',
            // 云服务商图片
            'img[src*="oss-cn"]',
            'img[src*="aliyuncs.com"]',
            'img[src*="qiniu"]',
            'img[src*="clouddn.com"]',
            'img[src*="cos."]',
            'img[src*="myqcloud.com"]',
            'img[src*="bytedance"]',
            'img[src*="byteimg"]',
            'img[src*="pstatp"]',
            // 特定网站图片
            'img[src*="jimeng"]',
            'img[src*="doubao"]',
            'img[src*="jianying"]',
            // 可能的容器内图片
            '[class*="image"] img',
            '[class*="result"] img',
            '[class*="generated"] img',
            '[class*="ai"] img',
            '[class*="chat"] img',
            '[class*="message"] img',
            '[id*="image"] img',
            '[id*="result"] img'
        ];

        // 使用Set避免重复处理
        const processedImages = new Set();

        // 首先处理所有img标签
        const allImages = document.querySelectorAll('img');
        allImages.forEach(img => {
            if (shouldProcessImage(img) && !processedImages.has(img)) {
                processedImages.add(img);
                processImage(img);
            }
        });

        // 然后处理特定选择器
        selectors.forEach(selector => {
            try {
                const images = document.querySelectorAll(selector);
                images.forEach(img => {
                    if (shouldProcessImage(img) && !processedImages.has(img)) {
                        processedImages.add(img);
                        processImage(img);
                    }
                });
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e);
            }
        });

        console.log(`处理了 ${processedImages.size} 张图片`);
    }

    // 判断是否应该处理这张图片
    function shouldProcessImage(img) {
        if (!img || !img.src) return false;

        // 检查图片尺寸
        const rect = img.getBoundingClientRect();
        if (rect.width < 50 || rect.height < 50) return false;

        // 检查图片是否可见
        const style = window.getComputedStyle(img);
        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
            return false;
        }

        // 排除一些不需要的图片
        const excludePatterns = [
            'avatar',
            'icon',
            'logo',
            'button',
            'loading',
            'spinner',
            'placeholder'
        ];

        const imgSrc = img.src.toLowerCase();
        const imgClass = (img.className || '').toLowerCase();
        const imgId = (img.id || '').toLowerCase();

        for (const pattern of excludePatterns) {
            if (imgSrc.includes(pattern) || imgClass.includes(pattern) || imgId.includes(pattern)) {
                return false;
            }
        }

        return true;
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // 元素节点
                        if (node.tagName === 'IMG') {
                            processImage(node);
                        } else {
                            // 查找新添加节点中的图片
                            const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                            images.forEach(processImage);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 添加右键菜单功能
    function addContextMenu() {
        document.addEventListener('contextmenu', function(e) {
            if (e.target.tagName === 'IMG' && e.target.src) {
                e.preventDefault();

                const menu = document.createElement('div');
                menu.style.cssText = `
                    position: fixed;
                    left: ${e.pageX}px;
                    top: ${e.pageY}px;
                    background: white;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    padding: 5px 0;
                    z-index: 10001;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                `;

                const downloadOption = document.createElement('div');
                downloadOption.style.cssText = `
                    padding: 8px 15px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #333;
                `;
                downloadOption.textContent = '下载原图';
                downloadOption.addEventListener('click', () => {
                    const originalUrl = getOriginalImageUrl(e.target);
                    const timestamp = new Date().getTime();
                    const filename = `image_${timestamp}.png`;
                    downloadImage(originalUrl, filename);
                    document.body.removeChild(menu);
                });

                downloadOption.addEventListener('mouseenter', () => {
                    downloadOption.style.background = '#f0f0f0';
                });
                downloadOption.addEventListener('mouseleave', () => {
                    downloadOption.style.background = 'white';
                });

                menu.appendChild(downloadOption);
                document.body.appendChild(menu);

                // 点击其他地方关闭菜单
                setTimeout(() => {
                    document.addEventListener('click', function closeMenu() {
                        if (menu.parentNode) {
                            document.body.removeChild(menu);
                        }
                        document.removeEventListener('click', closeMenu);
                    });
                }, 100);
            }
        });
    }

    // 添加快捷键支持
    function addKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+D 下载当前鼠标悬停的图片
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                const hoveredImg = document.querySelector('img:hover');
                if (hoveredImg && hoveredImg.src) {
                    const originalUrl = getOriginalImageUrl(hoveredImg);
                    const timestamp = new Date().getTime();
                    const filename = `image_${timestamp}.png`;
                    downloadImage(originalUrl, filename);
                }
            }
        });
    }

    // 添加调试功能
    function addDebugFeatures() {
        // 添加调试按钮
        const debugBtn = document.createElement('div');
        debugBtn.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            z-index: 10002;
        `;
        debugBtn.textContent = '调试模式';
        debugBtn.addEventListener('click', () => {
            const allImages = document.querySelectorAll('img');
            console.log(`页面总共有 ${allImages.length} 张图片`);

            allImages.forEach((img, index) => {
                console.log(`图片 ${index + 1}:`, {
                    src: img.src,
                    width: img.offsetWidth,
                    height: img.offsetHeight,
                    className: img.className,
                    id: img.id,
                    dataset: img.dataset,
                    shouldProcess: shouldProcessImage(img)
                });

                // 给图片添加红色边框以便识别
                if (shouldProcessImage(img)) {
                    img.style.border = '2px solid red';
                    setTimeout(() => {
                        img.style.border = '';
                    }, 3000);
                }
            });

            showNotification(`找到 ${allImages.length} 张图片，详情请查看控制台`);
        });

        document.body.appendChild(debugBtn);
    }

    // 初始化脚本
    function init() {
        console.log('即梦AI & 豆包去水印下载脚本已启动');

        addStyles();
        addDebugFeatures(); // 添加调试功能
        processAllImages();
        observeChanges();
        addContextMenu();
        addKeyboardShortcuts();

        // 定期检查新图片
        setInterval(processAllImages, 3000);

        // 显示启动通知
        showNotification('去水印下载脚本已启动！鼠标悬停图片显示下载按钮，点击左上角红色按钮进行调试');
    }

    // 等待页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 1000); // 延迟1秒确保页面完全加载
    }

})();
