// ==UserScript==
// @name         即梦AI & 豆包去水印原图下载
// @namespace    http://tampermonkey.net/
// @version      4.0
// @description  去除即梦AI和豆包的水印，支持原图下载
// <AUTHOR>
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 样式
    const styles = `
        .download-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 12px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .download-btn:hover {
            background: rgba(0, 0, 0, 0.4);
        }
        .image-container {
            position: relative;
            display: inline-block;
        }
        .image-container:hover .download-btn {
            opacity: 1;
        }
    `;

    // 添加样式
    function addStyles() {
        const style = document.createElement('style');
        style.textContent = styles;
        document.head.appendChild(style);
    }

    // 拦截图片请求
    const imageUrls = new Set();
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && /\.(jpg|jpeg|png|webp)/.test(url)) {
            imageUrls.add(url);
        }
        return originalFetch.apply(this, args);
    };

    // 获取原图URL
    function getOriginalImageUrl(img) {
        let url = img.src || img.dataset.src || img.dataset.original;

        // 从拦截的请求中查找
        for (const interceptedUrl of imageUrls) {
            if (interceptedUrl.includes('oss-cn') || interceptedUrl.includes('aliyuncs') ||
                interceptedUrl.includes('qiniu') || interceptedUrl.includes('bytedance')) {
                url = interceptedUrl;
                break;
            }
        }

        // 即梦AI处理
        if (window.location.hostname.includes('jimeng.jianying.com')) {
            url = url.replace(/[?&](x-oss-process|watermark|quality|format|resize)=[^&]*/g, '');
            if (!url.includes('?')) {
                url += '?x-oss-process=image/format,png/quality,q_100';
            }
        }

        // 豆包处理
        if (window.location.hostname.includes('doubao.com')) {
            url = url.replace(/[?&](x-image-process|watermark|quality|imageView2)=[^&]*/g, '');
            if (!url.includes('?')) {
                url += '?imageView2/2/format/png/q/100';
            }
        }

        return url;
    }

    // 下载图片
    async function downloadImage(url, filename) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Referer': window.location.href,
                    'User-Agent': navigator.userAgent
                }
            });

            if (!response.ok) throw new Error('下载失败');

            const blob = await response.blob();
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            link.click();
            URL.revokeObjectURL(link.href);

            showNotification(`下载成功！大小: ${(blob.size/1024/1024).toFixed(2)}MB`);
        } catch (error) {
            showNotification('下载失败，请重试', 'error');
        }
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }

    // 创建下载按钮
    function createDownloadButton(img) {
        const button = document.createElement('button');
        button.className = 'download-btn';
        button.textContent = '下载原图';
        button.onclick = (e) => {
            e.preventDefault();
            e.stopPropagation();
            const url = getOriginalImageUrl(img);
            const filename = `image_${Date.now()}.png`;
            downloadImage(url, filename);
        };
        return button;
    }

    // 处理图片
    function processImage(img) {
        if (img.dataset.processed) return;

        const container = document.createElement('div');
        container.className = 'image-container';

        img.parentNode.insertBefore(container, img);
        container.appendChild(img);
        container.appendChild(createDownloadButton(img));

        img.dataset.processed = 'true';
    }

    // 处理所有图片
    function processAllImages() {
        document.querySelectorAll('img').forEach(img => {
            if (img.src && img.offsetWidth > 100 && img.offsetHeight > 100 && !img.dataset.processed) {
                processImage(img);
            }
        });
    }

    // 初始化
    function init() {
        addStyles();
        setTimeout(processAllImages, 1000);
        setInterval(processAllImages, 2000);

        // 监听DOM变化
        new MutationObserver(processAllImages).observe(document.body, {
            childList: true,
            subtree: true
        });

        showNotification('去水印下载脚本已启动！');
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
