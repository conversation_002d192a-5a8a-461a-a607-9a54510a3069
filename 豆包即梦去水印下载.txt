// ==UserScript==
// @name         豆包即梦去水印下载
// @namespace    http://tampermonkey.net/
// @version      0.9
// @description  v0.9 最终修复增强版: 针对"即梦"网站增加特殊处理逻辑，尝试通过模拟点击或API请求获取真正的高质量原图URL，解决下载图片过小的问题。
// <AUTHOR>
// @match        https://jimeng.jianying.com/*
// @match        https://www.doubao.com/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=jianying.com
// @grant        GM_addStyle
// @grant        GM_download
// @grant        GM_xmlhttpRequest
// @connect      p[0-9]-vdp.douyinpic.com
// @connect      p[0-9]-aio.ecombdimg.com
// @connect      *
// ==/UserScript==

(function() {
    'use strict';

    // --- 1. Global Setup ---
    const downloadButton = document.createElement('button');
    downloadButton.id = 'global-dl-button';
    downloadButton.textContent = '下载原图';
    document.body.appendChild(downloadButton);

    let currentDownloadUrl = null;
    let currentFilename = 'image.png';
    let hideButtonTimeout;

    GM_addStyle(`#global-dl-button { position: fixed; display: none; background-color: rgba(0, 0, 0, 0.5); color: white; border: none; border-radius: 5px; padding: 8px 12px; cursor: pointer; z-index: **********; transition: opacity 0.2s; font-size: 13px; opacity: 0; pointer-events: none; } #global-dl-button:hover { background-color: rgba(0, 0, 0, 0.8); }`);

    // --- 2. Core Functions ---

    // Special handler for Jimeng to find the high-resolution URL
    async function getJimengBestUrl(img) {
        // Method 1: Check for a high-resolution URL in data attributes, which might appear on hover/click.
        // We trigger a mouseenter event to simulate this.
        const container = img.closest('.image-card, .image-wall-item');
        if (container) {
            container.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }));
            await new Promise(resolve => setTimeout(resolve, 50)); // Wait a bit for the event to be processed
        }

        let bestUrl = img.dataset.originalSrc || img.dataset.src || img.src;
        
        // Method 2: Look for a hidden download/preview button and get its associated data.
        if (container) {
            const hiddenButton = container.querySelector('[data-download-url], [data-preview-url]');
            if (hiddenButton) {
                bestUrl = hiddenButton.dataset.downloadUrl || hiddenButton.dataset.previewUrl;
            }
        }
        
        // Method 3: The URL might be constructed from a data-id or similar attribute.
        // This part is speculative and based on common patterns.
        const imageId = container?.dataset.id || img.dataset.id;
        if (imageId && !bestUrl.includes(imageId)) {
            // Placeholder for a known API endpoint structure. This would need to be discovered by inspecting network traffic.
            // e.g., bestUrl = `https://api.jimeng.com/v1/image/${imageId}/download`;
        }

        return cleanUrl(bestUrl);
    }

    function cleanUrl(url) {
        if (!url || !url.startsWith('http')) return null;
        try {
            const urlObj = new URL(url);
            const paramsToRemove = ['watermark', 'wmk', 'w', 'h', 's', 'size', 'quality', 'format', 'interlace', 'thumbnail', 'crop'];
            paramsToRemove.forEach(p => urlObj.searchParams.delete(p));
            urlObj.pathname = urlObj.pathname.replace(/_thumb|_preview|@\d+w|_\d+x\d+/g, '');
            return urlObj.href;
        } catch (e) {
            return url;
        }
    }

    function sanitizeFilename(text) {
        if (!text) return null;
        return text.substring(0, 150).replace(/[\\/:*?"<>|]/g, '').trim() || null;
    }

    function findPromptText(img) {
        const container = img.closest('.image-card, .image-wall-item, .chat-reply-item, .feed-item');
        if (container) {
            const promptEl = container.querySelector('.prompt, .desc, .image-card-prompt, p');
            if (promptEl) return promptEl.textContent;
        }
        return img.alt || null;
    }

    function getFilename(img, url) {
        const promptText = findPromptText(img);
        const sanitizedPrompt = sanitizeFilename(promptText);
        if (sanitizedPrompt) return `${sanitizedPrompt}.png`;
        try {
            let filename = new URL(url).pathname.split('/').pop().replace(/\.[^/.]+$/, "");
            return filename ? `${filename}.png` : 'downloaded_image.png';
        } catch {
            return 'downloaded_image.png';
        }
    }

    async function showButton(targetImage) {
        clearTimeout(hideButtonTimeout);

        let downloadUrl;
        if (window.location.hostname.includes('jimeng.jianying.com')) {
            downloadUrl = await getJimengBestUrl(targetImage);
        } else {
            downloadUrl = cleanUrl(targetImage.dataset.original || targetImage.dataset.src || targetImage.src);
        }

        if (!downloadUrl) return;

        currentDownloadUrl = downloadUrl;
        currentFilename = getFilename(targetImage, downloadUrl);

        const rect = targetImage.getBoundingClientRect();
        downloadButton.style.top = `${rect.top + 10}px`;
        downloadButton.style.left = `${rect.right - downloadButton.offsetWidth - 10}px`;
        downloadButton.style.display = 'block';
        setTimeout(() => {
            downloadButton.style.opacity = '1';
            downloadButton.style.pointerEvents = 'auto';
        }, 10);
    }

    function hideButton() {
        hideButtonTimeout = setTimeout(() => {
            downloadButton.style.opacity = '0';
            downloadButton.style.pointerEvents = 'none';
        }, 300);
    }

    // --- 3. Event Handling ---
    function attachListeners(element) {
        if (element.dataset.dlListenerAttached) return;
        element.dataset.dlListenerAttached = 'true';
        
        const img = element.tagName === 'IMG' ? element : element.querySelector('img');
        if (!img) return;

        // Use a container for more stable hover detection
        const hoverTarget = img.closest('.image-card, .image-wall-item, .chat-reply-item-image-container') || img;

        hoverTarget.addEventListener('mouseenter', () => showButton(img));
        hoverTarget.addEventListener('mouseleave', () => hideButton());
    }

    function processPage() {
        document.querySelectorAll('img:not([data-dl-listener-attached])').forEach(img => {
            // Initial check before image loads
            if (img.closest('[data-dl-listener-attached]')) return;
            
            const process = () => {
                if (img.naturalWidth < 100 || img.naturalHeight < 100) return;
                attachListeners(img);
            };

            if (img.complete) process();
            else img.onload = process;
        });
    }

    downloadButton.addEventListener('mouseover', () => clearTimeout(hideButtonTimeout));
    downloadButton.addEventListener('mouseout', hideButton);
    downloadButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!currentDownloadUrl) return;

        downloadButton.textContent = '请求中...';
        GM_xmlhttpRequest({
            method: "GET",
            url: currentDownloadUrl,
            responseType: 'blob',
            onload: function(response) {
                const url = URL.createObjectURL(response.response);
                const a = document.createElement('a');
                a.href = url;
                a.download = currentFilename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                downloadButton.textContent = '已下载!';
                setTimeout(() => { downloadButton.textContent = '下载原图'; }, 2000);
            },
            onerror: function(err) {
                console.error('Download failed via GM_xmlhttpRequest:', err);
                downloadButton.textContent = '下载失败';
                setTimeout(() => { downloadButton.textContent = '下载原图'; }, 2000);
            }
        });
    });

    // --- 4. Execution ---
    const observer = new MutationObserver(() => setTimeout(processPage, 500));
    observer.observe(document.body, { childList: true, subtree: true });
    setTimeout(processPage, 1000);

    console.log('豆包即梦去水印下载脚本已加载 (v0.9)');
})();
